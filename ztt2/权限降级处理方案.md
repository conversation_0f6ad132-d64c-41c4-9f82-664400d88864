# 权限降级处理方案

## 概述

当用户从付费会员（初级会员或高级会员）降级为免费用户时，需要妥善处理iCloud同步功能的关闭和数据保护，确保用户数据安全和良好的用户体验。

## 降级触发场景

### 1. 订阅过期
- 用户未及时续费，订阅自动过期
- 系统检测到订阅状态变为"free"

### 2. 主动取消订阅
- 用户在App Store中取消订阅
- 订阅在当前周期结束后失效

### 3. 退款处理
- 用户申请退款成功
- 系统立即撤销订阅权限

## 降级处理流程

### 第一阶段：检测降级
```swift
// 监听订阅状态变化
private func observeSubscriptionChanges() {
    dataManager.$currentUser
        .sink { [weak self] user in
            self?.handleSubscriptionChange(user: user)
        }
        .store(in: &cancellables)
}
```

### 第二阶段：数据备份
1. **创建云端数据备份**
   - 查询所有CloudKit记录
   - 记录数据统计信息
   - 确保数据完整性

2. **本地数据验证**
   - 检查本地Core Data数据
   - 验证关键数据完整性
   - 确保数据可用性

### 第三阶段：功能关闭
1. **关闭iCloud同步**
   - 停止CloudKit同步
   - 切换到本地存储模式
   - 更新用户设置

2. **UI状态更新**
   - 隐藏同步开关
   - 显示降级通知
   - 更新功能可用性

### 第四阶段：用户通知
1. **即时通知**
   - 显示降级横幅
   - 说明数据保护措施
   - 提供续费选项

2. **详细说明**
   - 数据保留政策
   - 功能限制说明
   - 恢复方法指导

## 数据保护策略

### 本地数据处理
- ✅ **完全保留**：所有本地数据保持不变
- ✅ **继续可用**：用户可正常使用本地功能
- ✅ **数据安全**：不会删除或损坏任何数据

### 云端数据处理
- ✅ **保留30天**：CloudKit数据保留30天
- ✅ **可恢复**：重新订阅后可恢复同步
- ⚠️ **自动清理**：30天后系统自动清理

### 数据同步状态
- ❌ **停止同步**：立即停止新数据同步
- ❌ **禁用上传**：不再上传本地变更
- ❌ **禁用下载**：不再下载云端变更

## 用户体验设计

### 降级通知界面
```swift
// 降级横幅
DowngradeBannerView(
    onRenewTapped: { showSubscriptionView = true },
    onDismiss: { showDowngradeBanner = false }
)
```

### 通知内容
- **标题**：会员已过期
- **说明**：iCloud同步已关闭，数据仅保存在本设备
- **操作**：续费按钮 + 关闭按钮

### 数据保护说明
- **本地数据安全**：数据已安全保存在本设备
- **云端数据保留**：云端数据将保留30天
- **恢复同步**：续费后可重新开启同步

## 错误处理机制

### 降级过程错误
1. **备份失败**
   - 记录错误日志
   - 继续执行降级
   - 通知用户风险

2. **验证失败**
   - 重试验证过程
   - 记录详细错误
   - 确保数据安全

3. **同步关闭失败**
   - 强制关闭同步
   - 清理相关设置
   - 防止数据冲突

### 用户操作错误
1. **网络异常**
   - 离线处理降级
   - 延迟云端操作
   - 保证本地功能

2. **存储空间不足**
   - 跳过备份步骤
   - 优先保护本地数据
   - 提示用户清理空间

## 恢复机制

### 重新订阅后
1. **自动检测**：系统自动检测订阅恢复
2. **功能恢复**：重新显示iCloud同步选项
3. **用户选择**：用户手动决定是否开启同步
4. **数据合并**：智能合并本地和云端数据

### 数据冲突处理
1. **时间戳比较**：以最新修改时间为准
2. **用户选择**：重要冲突让用户决定
3. **备份保留**：保留冲突数据的备份

## 监控和日志

### 关键指标
- 降级处理成功率
- 数据备份完成率
- 用户续费转化率
- 错误发生频率

### 日志记录
```swift
print("⬇️ 检测到订阅降级，开始处理...")
print("💾 创建云端数据备份...")
print("✅ 订阅降级处理完成")
```

## 测试场景

### 功能测试
1. **正常降级**：订阅过期后的标准流程
2. **网络异常**：降级过程中网络中断
3. **存储异常**：设备存储空间不足
4. **快速切换**：短时间内多次订阅/取消

### 数据测试
1. **数据完整性**：降级前后数据对比
2. **功能可用性**：本地功能正常使用
3. **恢复测试**：重新订阅后数据恢复

### 用户体验测试
1. **通知及时性**：降级通知是否及时显示
2. **操作流畅性**：续费流程是否顺畅
3. **信息清晰性**：用户是否理解降级影响

## 最佳实践

### 开发建议
1. **优雅降级**：确保降级过程不影响用户使用
2. **数据优先**：始终优先保护用户数据
3. **透明沟通**：清楚告知用户降级影响
4. **简化恢复**：让用户容易重新开启功能

### 运营建议
1. **提前通知**：订阅到期前提醒用户
2. **优惠激励**：提供续费优惠促进转化
3. **功能引导**：突出同步功能的价值
4. **客服支持**：提供降级相关的客服帮助

## 总结

权限降级处理是一个复杂但重要的功能，需要平衡数据安全、用户体验和业务目标。通过完善的降级流程、清晰的用户沟通和可靠的恢复机制，可以最大化用户满意度和续费转化率。
