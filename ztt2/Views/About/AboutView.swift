//
//  AboutView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/3.
//

import SwiftUI

/**
 * 关于页面
 */
struct AboutView: View {
    
    @Environment(\.dismiss) private var dismiss
    @State private var showUserAgreement = false
    @State private var showPrivacyPolicy = false
    @State private var showChildrenPrivacyPolicy = false
    @State private var pageAppeared = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景渐变 - 与其他页面保持一致
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(hex: "#f8fdf0"),
                        Color(hex: "#f0f8e0")
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea(.all)
                
                // 装饰性背景元素
                VStack {
                    HStack {
                        Circle()
                            .fill(Color(hex: "#B5E36B").opacity(0.03))
                            .frame(width: 80, height: 80)
                            .offset(x: -20, y: 10)
                        Spacer()
                        Circle()
                            .fill(Color(hex: "#FFE49E").opacity(0.04))
                            .frame(width: 100, height: 100)
                            .offset(x: 30, y: -5)
                    }
                    Spacer()
                    HStack {
                        Spacer()
                        Circle()
                            .fill(Color(hex: "#B5E36B").opacity(0.02))
                            .frame(width: 60, height: 60)
                            .offset(x: 15, y: 20)
                    }
                }
                
                VStack(spacing: 0) {
                    // 顶部空间
                    Spacer()
                        .frame(height: 60)

                    // 政策选项列表
                    VStack(spacing: 0) {
                        AboutItemView(
                            title: "settings.item.user_agreement".localized,
                            iconName: "doc.text",
                            isFirst: true
                        ) {
                            showUserAgreement = true
                        }
                        .opacity(pageAppeared ? 1.0 : 0.0)
                        .offset(y: pageAppeared ? 0 : 30)
                        .animation(.easeOut(duration: 0.6).delay(0.3), value: pageAppeared)

                        AboutItemView(
                            title: "settings.item.privacy_policy".localized,
                            iconName: "hand.raised"
                        ) {
                            showPrivacyPolicy = true
                        }
                        .opacity(pageAppeared ? 1.0 : 0.0)
                        .offset(y: pageAppeared ? 0 : 30)
                        .animation(.easeOut(duration: 0.6).delay(0.4), value: pageAppeared)

                        AboutItemView(
                            title: "settings.item.children_privacy_policy".localized,
                            iconName: "figure.and.child.holdinghands"
                        ) {
                            showChildrenPrivacyPolicy = true
                        }
                        .opacity(pageAppeared ? 1.0 : 0.0)
                        .offset(y: pageAppeared ? 0 : 30)
                        .animation(.easeOut(duration: 0.6).delay(0.5), value: pageAppeared)
                    }
                    .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)

                    Spacer()

                    // 版本信息 - 底部居中
                    VStack(spacing: 8) {
                        Text("版本信息")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textSecondary)

                        Text("v\(appVersion)")
                            .font(.system(size: 20, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)

                        // 备案号链接
                        Button(action: {
                            if let url = URL(string: "https://beian.miit.gov.cn/#/Integrated/recordQuery") {
                                UIApplication.shared.open(url)
                            }
                        }) {
                            Text("粤ICP备2025394023号-6A")
                                .font(.system(size: 12, weight: .regular))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                    }
                    .opacity(pageAppeared ? 1.0 : 0.0)
                    .offset(y: pageAppeared ? 0 : 20)
                    .animation(.easeOut(duration: 0.6).delay(0.6), value: pageAppeared)
                    .padding(.bottom, 40)
                }
                .padding(.top, DesignSystem.Spacing.lg)
            }
            .navigationTitle("关于")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                    }
                }
            }
        }
        .onAppear {
            withAnimation {
                pageAppeared = true
            }
        }
        .sheet(isPresented: $showUserAgreement) {
            UserAgreementView()
        }
        .sheet(isPresented: $showPrivacyPolicy) {
            PrivacyPolicyView()
        }
        .sheet(isPresented: $showChildrenPrivacyPolicy) {
            ChildrenPrivacyPolicyView()
        }
    }
    
    // 获取应用版本号
    private var appVersion: String {
        Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
    }
    
    // 获取构建号
    private var buildNumber: String {
        Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
    }
}

/**
 * 关于页面选项视图
 */
struct AboutItemView: View {
    let title: String
    let iconName: String
    let isFirst: Bool
    let action: () -> Void
    
    @State private var isPressed = false
    
    init(title: String, iconName: String, isFirst: Bool = false, action: @escaping () -> Void) {
        self.title = title
        self.iconName = iconName
        self.isFirst = isFirst
        self.action = action
    }
    
    var body: some View {
        Button(action: {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                isPressed = true
                action()
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                isPressed = false
            }
        }) {
            HStack(spacing: 16) {
                // 左侧圆形图标背景
                ZStack {
                    Circle()
                        .fill(Color(hex: "#8BC34A"))
                        .frame(width: 40, height: 40)

                    Image(systemName: iconName)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white)
                }

                // 标题文字
                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()

                // 右侧箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(Color.clear)
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
    }
}

// MARK: - Preview
#Preview {
    AboutView()
}
