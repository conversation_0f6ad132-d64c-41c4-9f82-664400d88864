//
//  SubscriptionDowngradeHandler.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/3.
//

import SwiftUI
import Combine

/**
 * 订阅降级处理组件
 * 监听降级通知并显示相应的用户界面
 */
struct SubscriptionDowngradeHandler: ViewModifier {
    
    @State private var showDowngradeAlert = false
    @State private var downgradeMessage = ""
    @State private var showSubscriptionView = false
    
    func body(content: Content) -> some View {
        content
            .onReceive(NotificationCenter.default.publisher(for: .subscriptionDowngraded)) { notification in
                handleDowngradeNotification(notification)
            }
            .onReceive(NotificationCenter.default.publisher(for: .subscriptionUpgraded)) { notification in
                handleUpgradeNotification(notification)
            }
            .alert("subscription_downgrade.alert.title".localized, isPresented: $showDowngradeAlert) {
                Button("subscription_downgrade.alert.renew".localized) {
                    showSubscriptionView = true
                }
                Button("subscription_downgrade.alert.later".localized, role: .cancel) {
                    // 用户选择稍后处理
                }
            } message: {
                Text("subscription_downgrade.alert.message".localized)
            }
            .fullScreenCover(isPresented: $showSubscriptionView) {
                SubscriptionView {
                    showSubscriptionView = false
                }
            }
    }
    
    private func handleDowngradeNotification(_ notification: Notification) {
        if let userInfo = notification.userInfo,
           let message = userInfo["message"] as? String {
            downgradeMessage = message
            showDowngradeAlert = true
        }
    }
    
    private func handleUpgradeNotification(_ notification: Notification) {
        // 可以在这里显示升级成功的提示
        print("🎉 用户订阅升级成功")
    }
}

// MARK: - View Extension

extension View {
    func handleSubscriptionChanges() -> some View {
        modifier(SubscriptionDowngradeHandler())
    }
}

/**
 * 降级状态横幅组件
 * 在降级后显示在界面顶部
 */
struct DowngradeBannerView: View {
    
    let onRenewTapped: () -> Void
    let onDismiss: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 警告图标
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(.orange)
                .font(.system(size: 16, weight: .medium))
            
            // 提示文本
            VStack(alignment: .leading, spacing: 2) {
                Text("会员已过期")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.primary)
                
                Text("iCloud同步已关闭，数据仅保存在本设备")
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 续费按钮
            Button("续费") {
                onRenewTapped()
            }
            .font(.system(size: 12, weight: .medium))
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(Color.orange)
            .cornerRadius(6)
            
            // 关闭按钮
            Button(action: onDismiss) {
                Image(systemName: "xmark")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.orange.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

/**
 * 数据保护说明组件
 * 解释降级后的数据处理方式
 */
struct DataProtectionInfoView: View {
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "shield.checkered")
                    .foregroundColor(.green)
                    .font(.system(size: 16, weight: .medium))
                
                Text("数据保护说明")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.primary)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                DataProtectionItem(
                    icon: "checkmark.circle.fill",
                    color: .green,
                    title: "本地数据安全",
                    description: "您的所有数据已安全保存在本设备，不会丢失"
                )
                
                DataProtectionItem(
                    icon: "icloud.fill",
                    color: .blue,
                    title: "云端数据保留",
                    description: "云端数据将保留30天，重新订阅后可恢复同步"
                )
                
                DataProtectionItem(
                    icon: "arrow.clockwise",
                    color: .orange,
                    title: "恢复同步",
                    description: "续费会员后可重新开启iCloud同步功能"
                )
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

/**
 * 数据保护项目组件
 */
private struct DataProtectionItem: View {
    let icon: String
    let color: Color
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(color)
                .font(.system(size: 14, weight: .medium))
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.primary)
                
                Text(description)
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        DowngradeBannerView(
            onRenewTapped: {
                print("续费按钮被点击")
            },
            onDismiss: {
                print("关闭按钮被点击")
            }
        )
        
        DataProtectionInfoView()
    }
    .padding()
}
