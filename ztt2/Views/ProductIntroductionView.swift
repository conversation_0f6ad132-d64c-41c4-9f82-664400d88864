//
//  ProductIntroductionView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/2.
//

import SwiftUI

/**
 * 产品介绍页面
 * 展示转团团应用的详细介绍和联系方式
 */
struct ProductIntroductionView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var pageAppeared = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景渐变 - 与其他页面保持一致
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(hex: "#f8fdf0"),
                        Color(hex: "#f0f8e0")
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()

                // 装饰性背景元素
                GeometryReader { geometry in
                    // 左上角装饰圆
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(hex: "#a9d051").opacity(0.1),
                                    Color(hex: "#8bb83f").opacity(0.05)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 120, height: 120)
                        .position(x: -20, y: 50)

                    // 右下角装饰圆
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(hex: "#8bb83f").opacity(0.08),
                                    Color(hex: "#a9d051").opacity(0.03)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 150, height: 150)
                        .position(x: geometry.size.width + 30, y: geometry.size.height - 50)
                }

                // 主要内容
                ScrollView(.vertical, showsIndicators: false) {
                    VStack(spacing: DesignSystem.Spacing.xl) {
                        // 顶部空间
                        Spacer()
                            .frame(height: DesignSystem.Spacing.md)

                        // 应用标题和图标
                        VStack(spacing: DesignSystem.Spacing.lg) {
                            // 应用图标
                            Image("logo")
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 80, height: 80)
                                .clipShape(RoundedRectangle(cornerRadius: 16))
                                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                                .opacity(pageAppeared ? 1.0 : 0.0)
                                .scaleEffect(pageAppeared ? 1.0 : 0.8)
                                .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.1), value: pageAppeared)

                            // 应用名称
                            Text("转团团")
                                .font(.system(
                                    size: DesignSystem.Typography.HeadingLarge.fontSize,
                                    weight: DesignSystem.Typography.HeadingLarge.fontWeight
                                ))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                                .opacity(pageAppeared ? 1.0 : 0.0)
                                .offset(y: pageAppeared ? 0 : -20)
                                .animation(.easeOut(duration: 0.6).delay(0.2), value: pageAppeared)

                            // 副标题
                            Text("让成长更有意义的家庭积分管理应用")
                                .font(.system(
                                    size: DesignSystem.Typography.Body.fontSize,
                                    weight: DesignSystem.Typography.Body.fontWeight
                                ))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .multilineTextAlignment(.center)
                                .opacity(pageAppeared ? 1.0 : 0.0)
                                .offset(y: pageAppeared ? 0 : -10)
                                .animation(.easeOut(duration: 0.6).delay(0.3), value: pageAppeared)
                        }

                        // 产品介绍内容
                        ProductIntroductionContent()
                            .opacity(pageAppeared ? 1.0 : 0.0)
                            .offset(y: pageAppeared ? 0 : 30)
                            .animation(.easeOut(duration: 0.8).delay(0.4), value: pageAppeared)

                        // 联系方式
                        ContactSection()
                            .opacity(pageAppeared ? 1.0 : 0.0)
                            .offset(y: pageAppeared ? 0 : 30)
                            .animation(.easeOut(duration: 0.8).delay(0.6), value: pageAppeared)

                        // 底部空间
                        Spacer()
                            .frame(height: DesignSystem.Spacing.xl)
                    }
                    .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                }
            }
            .navigationTitle("产品介绍")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                    }
                }
            }
        }
        .onAppear {
            withAnimation {
                pageAppeared = true
            }
        }
    }
}

/**
 * 产品介绍内容组件
 */
private struct ProductIntroductionContent: View {
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.xl) {
            // 从山区课堂到千万家庭的教育智慧
            IntroductionSection(
                title: "从山区课堂到千万家庭的教育智慧",
                icon: "graduationcap.fill",
                content: """
                作为一名山区支教老师，我深深体会到积分管理在教育中的神奇力量。最初，我用小本本记录每个学生的行为表现——加分、扣分、兑换奖品。孩子们用自己的努力"赚取"文具和小礼品时，那种发自内心的珍惜和自豪，让我看到了教育的真谛。后来借助AI编程技术，我将这套行之有效的方法打造成了"团团转"应用，帮助更多老师轻松管理班级。
                """
            )

            // 家庭教育的新思路
            IntroductionSection(
                title: "家庭教育的新思路",
                icon: "house.fill",
                content: """
                积分制不仅适用于课堂，在家庭教育中同样威力无穷。想象一下：

                • 孩子想要新玩具或自行车时，不再是简单的"买买买"
                • 背诵一首古诗得1分，准时起床得1分，主动做家务得2分...
                • 犯错时不需要打骂，批评教育加适当扣分更有效
                • 孩子用积分兑换心仪物品时，那份成就感和被尊重的感觉无价

                这样获得的东西，孩子会格外珍惜，因为这是他们用努力"赚"来的。而父母也在这个过程中，学会了更好地管理自己的情绪和行为，为孩子树立榜样。

                更有趣的是，转团团还引入了全家角色系统，让父母也参与到积分制当中：
                • 妈妈今天做饭特别香，全家一致通过加5分
                • 爸爸把工作坏情绪带回家，被"罚"扣10分
                • 父母可以用自己的积分"雇佣"孩子完成任务或帮忙做事
                • 全家人都在同一个积分体系中，增加了无穷家庭乐趣
                """
            )

            // 不只是积分管理，更是成长伙伴
            IntroductionSection(
                title: "不只是积分管理，更是成长伙伴",
                icon: "heart.fill",
                content: """
                转团团在保留大转盘、盲盒、刮刮卡等趣味抽奖功能的基础上，特别增加了成长日记功能：

                • 记录孩子成长路上的珍贵瞬间
                • AI智能分析生成个性化成长报告
                • 为家长提供科学、实用的教育建议

                让每一次进步都被看见，让每一份努力都有回响。转团团，陪伴孩子健康快乐成长，让家庭教育更有温度、更有智慧。
                """
            )
        }
    }
}

/**
 * 介绍章节组件
 */
private struct IntroductionSection: View {
    let title: String
    let icon: String
    let content: String

    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.lg) {
            // 标题区域
            HStack(spacing: DesignSystem.Spacing.md) {
                // 图标
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(hex: "#a9d051").opacity(0.2),
                                    Color(hex: "#8bb83f").opacity(0.1)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 40, height: 40)

                    Image(systemName: icon)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(Color(hex: "#a9d051"))
                }

                // 标题
                Text(title)
                    .font(.system(
                        size: DesignSystem.Typography.HeadingMedium.fontSize,
                        weight: DesignSystem.Typography.HeadingMedium.fontWeight
                    ))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()
            }

            // 内容
            Text(content)
                .font(.system(
                    size: DesignSystem.Typography.Caption.fontSize,
                    weight: DesignSystem.Typography.Caption.fontWeight
                ))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .lineSpacing(6)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(DesignSystem.Spacing.lg)
        .background(
            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                .fill(DesignSystem.Colors.cardBackground)
                .shadow(color: DesignSystem.Shadow.light, radius: DesignSystem.Shadow.radius, x: 0, y: DesignSystem.Shadow.offsetY)
        )
    }
}

/**
 * 联系方式组件
 */
private struct ContactSection: View {
    @State private var showCopyAlert = false
    @State private var isPressed = false

    var body: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            // 标题
            Text("加入我们的家长交流群")
                .font(.system(
                    size: DesignSystem.Typography.HeadingMedium.fontSize,
                    weight: DesignSystem.Typography.HeadingMedium.fontWeight
                ))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .frame(maxWidth: .infinity, alignment: .leading)

            // 描述
            Text("我为本应用的家长用户组建了一个群，大家可以在群里讨论和分享家庭教育的方法和经验，同时也可以为本应用提供更多改进的建议。欢迎您的加入！")
                .font(.system(
                    size: DesignSystem.Typography.Caption.fontSize,
                    weight: DesignSystem.Typography.Caption.fontWeight
                ))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .lineSpacing(6)
                .frame(maxWidth: .infinity, alignment: .leading)

            // 联系邮箱按钮
            Button(action: {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    isPressed = true
                    copyEmailToClipboard()
                }

                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    isPressed = false
                }
            }) {
                HStack(spacing: DesignSystem.Spacing.sm) {
                    // 邮箱图标
                    ZStack {
                        Circle()
                            .fill(Color.white.opacity(0.2))
                            .frame(width: 32, height: 32)

                        Image(systemName: "envelope.fill")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                    }

                    Text("<EMAIL>")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)

                    Spacer()

                    // 复制图标
                    Image(systemName: "doc.on.doc")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
                .padding(DesignSystem.Spacing.md)
                .background(
                    RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(hex: "#a9d051"),
                                    Color(hex: "#8bb83f")
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
        }
        .padding(DesignSystem.Spacing.lg)
        .background(
            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                .fill(DesignSystem.Colors.cardBackground)
                .shadow(color: DesignSystem.Shadow.light, radius: DesignSystem.Shadow.radius, x: 0, y: DesignSystem.Shadow.offsetY)
        )
        .alert("邮箱已复制", isPresented: $showCopyAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text("邮箱地址已复制到剪贴板")
        }
    }

    private func copyEmailToClipboard() {
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        UIPasteboard.general.string = "<EMAIL>"
        showCopyAlert = true
    }
}

// MARK: - Preview
#Preview {
    ProductIntroductionView()
}
