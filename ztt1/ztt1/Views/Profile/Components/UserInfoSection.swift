//
//  UserInfoSection.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 个人信息组件
 * 显示用户头像、姓名、会员状态、ID、到期时间和插图
 */
struct UserInfoSection: View {
    
    // MARK: - Properties
    let userName: String
    let userID: String
    let membershipLevel: String
    let expirationDate: String
    
    // MARK: - State
    @State private var sectionAppeared = false
    
    var body: some View {
        ZStack {
            // 美化背景容器
            RoundedRectangle(cornerRadius: 30)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            DesignSystem.Colors.profileUserInfoBackground,
                            DesignSystem.Colors.profileUserInfoBackground.opacity(0.9)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .ignoresSafeArea(.all, edges: [.top, .leading, .trailing])
                .shadow(color: Color(hex: "#a9d051").opacity(0.15), radius: 8, x: 0, y: 4)
                .overlay(
                    RoundedRectangle(cornerRadius: 30)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
            
            // 装饰性背景元素
            Circle()
                .fill(Color(hex: "#B5E36B").opacity(0.06))
                .frame(width: 80, height: 80)
                .offset(x: 100, y: -60)
            
            Circle()
                .fill(Color(hex: "#FFE49E").opacity(0.05))
                .frame(width: 60, height: 60)
                .offset(x: -120, y: 80)
            
            Circle()
                .fill(Color.white.opacity(0.3))
                .frame(width: 40, height: 40)
                .offset(x: 130, y: 60)
            
            // 用户头像 - 左上角绝对定位
            HStack {
                VStack {
                    ZStack {
                        Circle()
                            .fill(Color.white)
                            .frame(width: 70, height: 70) 
                            .shadow(color: Color.black.opacity(0.08), radius: 4, x: 0, y: 2)
                        
                        // 头像背景光圈
                        Circle()
                            .fill(
                                RadialGradient(
                                    gradient: Gradient(colors: [
                                        Color.white.opacity(0.3),
                                        Color.clear
                                    ]),
                                    center: .center,
                                    startRadius: 0,
                                    endRadius: 35
                                )
                            )
                            .frame(width: 70, height: 70)
                        
                        Image("laoshi")
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(
                                width: DesignSystem.ProfilePage.UserInfoCard.avatarSize,
                                height: DesignSystem.ProfilePage.UserInfoCard.avatarSize
                            )
                            .background(
                                Circle()
                                    .fill(Color.white.opacity(0.5))
                            )
                            .clipShape(Circle())
                            .overlay(
                                Circle()
                                    .stroke(Color.white.opacity(0.3), lineWidth: 2)
                            )
                            .shadow(color: Color.black.opacity(0.1), radius: 3, x: 0, y: 2)
                    }
                    .padding(.top, 80) // 为状态栏留出空间
                    .padding(.leading, DesignSystem.Spacing.pageHorizontal)
                    .offset(y: -15) // 向上移动20个点
                    .scaleEffect(sectionAppeared ? 1.0 : 0.8)
                    .opacity(sectionAppeared ? 1.0 : 0.0)
                    .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: sectionAppeared)
                    Spacer()
                }
                Spacer()
            }
            
            // 用户信息 - 中左区域绝对定位
            HStack {
                VStack(alignment: .leading, spacing: 6) {
                    Spacer()
                    
                    // 姓名和会员标签
                    HStack(spacing: 8) {
                        Text(userName)
                            .font(.system(size: 20, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.profileSettingsTextColor)
                            .shadow(color: Color.white.opacity(0.5), radius: 1, x: 0, y: 1)
                        
                        // 会员等级标签
                        Text("(\(membershipLevel))")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 3)
                            .background(Color.white.opacity(0.7))
                            .cornerRadius(8)
                            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
                    }

                    // 会员到期时间
                    Text("profile.user_info.membership_expires".localized(with: expirationDate))
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    Spacer()
                }
                .padding(.leading, DesignSystem.Spacing.pageHorizontal + 90)
                .padding(.bottom, 30)
                .opacity(sectionAppeared ? 1.0 : 0.0)
                .offset(x: sectionAppeared ? 0 : -20, y: 20)
                .animation(.easeOut(duration: 0.8).delay(0.3), value: sectionAppeared)
                
                Spacer()
            }
            

        }
        .onAppear {
            withAnimation {
                sectionAppeared = true
            }
        }
    }
}

// MARK: - Preview
#Preview {
    UserInfoSection(
        userName: "老师",
        userID: "123456",
        membershipLevel: "高级会员",
        expirationDate: "2025-8-31"
    )
    .padding()
    .background(DesignSystem.Colors.background)
} 